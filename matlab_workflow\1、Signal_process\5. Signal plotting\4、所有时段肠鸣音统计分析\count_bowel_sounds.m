%% 肠鸣音统计分析程序
% 功能：自动统计和分析不同时间段内各类型肠鸣音的数量分布
% 输入：'全部数据'文件夹中按时间段组织的.mat文件（包含SB、MB、CRS类型标识）
% 输出：控制台统计结果、Excel文件（肠鸣音统计结果.xlsx）、可视化图表
% 处理步骤：
%   1. 遍历上午/下午时间段的第一次/第二次测量数据
%   2. 按5分钟时间段统计各类型肠鸣音数量（SB、MB、CRS）
%   3. 合并同一时间段的两次测量结果
%   4. 生成Excel报告和柱状图可视化
%   5. 创建10分钟间隔的整体活动趋势图
% 应用场景：医学研究中肠鸣音信号的时间分布分析和统计报告生成
% 数据结构：按照上午/下午、第一次/第二次、5分钟时间段进行组织

clear all;
clc;
close all;

% 自定义排序函数
function sorted_folders = sort_data_folders(folders)
    % 提取文件夹名中的数字
    numbers = zeros(length(folders), 1);
    for i = 1:length(folders)
        % 使用正则表达式提取数字
        name = folders(i).name;
        match = regexp(name, 'data(\d+)_', 'tokens');
        if ~isempty(match)
            numbers(i) = str2double(match{1}{1});
        end
    end
    
    % 根据提取的数字排序
    [~, idx] = sort(numbers);
    sorted_folders = folders(idx);
end

% 定义根目录
root_dir = '全部数据';

% 定义时间段（上午/下午）
time_periods = {'上午', '下午'};
time_periods_field = {'morning', 'afternoon'}; % 用于结构体字段名

% 定义测量次数
measurements = {'第一次', '第二次'};
measurements_field = {'first', 'second'}; % 用于结构体字段名

% 初始化结果存储结构
results = struct();

% 遍历上午和下���
for period_idx = 1:length(time_periods)
    period = time_periods{period_idx};
    period_field = time_periods_field{period_idx};
    results.(period_field) = struct();
    
    % 先获取两次测量的总文件夹数
    total_folders = 0;
    folder_counts = zeros(1, length(measurements));
    
    for meas_idx = 1:length(measurements)
        meas = measurements{meas_idx};
        current_path = fullfile(root_dir, period, meas);
        data_folders = dir(fullfile(current_path, 'data*_5min_tt'));
        folder_counts(meas_idx) = length(data_folders);
        total_folders = total_folders + length(data_folders);
    end
    
    % 遍历第一次和第二次
    for meas_idx = 1:length(measurements)
        meas = measurements{meas_idx};
        meas_field = measurements_field{meas_idx};
        
        % 获取当前处理路径
        current_path = fullfile(root_dir, period, meas);
        
        % 获取所有5分钟数据文件夹并排序
        data_folders = dir(fullfile(current_path, 'data*_5min_tt'));
        data_folders = sort_data_folders(data_folders);
        
        % 初始化当前测量的结果数组
        n_folders = length(data_folders);
        time_results = struct('SB', zeros(total_folders, 1), ...
                            'MB', zeros(total_folders, 1), ...
                            'CRS', zeros(total_folders, 1));
        
        % 计算起始索引
        start_idx = 1;
        if meas_idx > 1
            start_idx = sum(folder_counts(1:meas_idx-1)) + 1;
        end
        
        % 遍历每个5分钟文件夹
        for folder_idx = 1:n_folders
            folder_name = data_folders(folder_idx).name;
            folder_path = fullfile(current_path, folder_name);
            
            % 计算实际的存储索引
            actual_idx = start_idx + folder_idx - 1;
            
            % 获取文件夹中的所有.mat文件
            mat_files = dir(fullfile(folder_path, '*.mat'));
            
            % 统计各类型数量
            for file_idx = 1:length(mat_files)
                file_name = mat_files(file_idx).name;
                
                % 根据文件名统计类型
                if contains(file_name, '_SB_')
                    time_results.SB(actual_idx) = time_results.SB(actual_idx) + 1;
                elseif contains(file_name, '_MB_')
                    time_results.MB(actual_idx) = time_results.MB(actual_idx) + 1;
                elseif contains(file_name, '_CRS_')
                    time_results.CRS(actual_idx) = time_results.CRS(actual_idx) + 1;
                end
            end
            
            % 显示进度
            fprintf('%s %s - 处理文件夹 %s: SB=%d, MB=%d, CRS=%d\n', ...
                period, meas, folder_name, ...
                time_results.SB(actual_idx), ...
                time_results.MB(actual_idx), ...
                time_results.CRS(actual_idx));
        end
        
        % 保存当前测量的结果
        results.(period_field).(meas_field) = time_results;
    end
end

% 显示结果
for period_idx = 1:length(time_periods)
    period = time_periods{period_idx};
    period_field = time_periods_field{period_idx};
    fprintf('\n=== %s统计结果 ===\n', period);
    
    % 获取总文件夹数
    total_folders = 0;
    for meas_idx = 1:length(measurements)
        meas = measurements{meas_idx};
        current_path = fullfile(root_dir, period, meas);
        data_folders = dir(fullfile(current_path, 'data*_5min_tt'));
        total_folders = total_folders + length(data_folders);
    end
    
    % 显示结果
    fprintf('时间段\t\tSB\tMB\tCRS\n');
    for folder_idx = 1:total_folders
        % 计算实际的时间范围
        start_time = (folder_idx-1)*5;
        end_time = folder_idx*5;
        fprintf('%d-%.0f分钟\t%d\t%d\t%d\n', ...
            start_time, end_time, ...
            results.(period_field).first.SB(folder_idx) + results.(period_field).second.SB(folder_idx), ...
            results.(period_field).first.MB(folder_idx) + results.(period_field).second.MB(folder_idx), ...
            results.(period_field).first.CRS(folder_idx) + results.(period_field).second.CRS(folder_idx));
    end
end

% 保存结果到Excel文件
filename = '肠鸣音统计结果.xlsx';
for period_idx = 1:length(time_periods)
    period = time_periods{period_idx};
    period_field = time_periods_field{period_idx};
    
    % 获取总文件夹数
    total_folders = 0;
    for meas_idx = 1:length(measurements)
        meas = measurements{meas_idx};
        current_path = fullfile(root_dir, period, meas);
        data_folders = dir(fullfile(current_path, 'data*_5min_tt'));
        total_folders = total_folders + length(data_folders);
    end
    
    % 创建表格数据
    data = cell(total_folders + 1, 4);
    data(1,:) = {'时间段', 'SB', 'MB', 'CRS'};
    
    for folder_idx = 1:total_folders
        data{folder_idx+1, 1} = sprintf('%d-%.0f分钟', (folder_idx-1)*5, folder_idx*5);
        data{folder_idx+1, 2} = results.(period_field).first.SB(folder_idx) + results.(period_field).second.SB(folder_idx);
        data{folder_idx+1, 3} = results.(period_field).first.MB(folder_idx) + results.(period_field).second.MB(folder_idx);
        data{folder_idx+1, 4} = results.(period_field).first.CRS(folder_idx) + results.(period_field).second.CRS(folder_idx);
    end
    
    % 写入Excel
    writecell(data, filename, 'Sheet', period);
end

fprintf('\n结果已保存到文件：%s\n', filename);

% 绘制统计图表
figure('Name', '肠鸣音统计结果', 'Position', [************ 800]);

for period_idx = 1:length(time_periods)
    period = time_periods{period_idx};
    period_field = time_periods_field{period_idx};
    
    % 创建子图
    subplot(2, 1, period_idx);
    
    % 获取总文件夹数
    total_folders = 0;
    for meas_idx = 1:length(measurements)
        meas = measurements{meas_idx};
        current_path = fullfile(root_dir, period, meas);
        data_folders = dir(fullfile(current_path, 'data*_5min_tt'));
        total_folders = total_folders + length(data_folders);
    end
    
    % 准备数据
    x = 1:total_folders;
    bar_width = 0.25;
    
    % 合并第一次和第二次的数据
    combined_data = struct();
    combined_data.SB = results.(period_field).first.SB + results.(period_field).second.SB;
    combined_data.MB = results.(period_field).first.MB + results.(period_field).second.MB;
    combined_data.CRS = results.(period_field).first.CRS + results.(period_field).second.CRS;
    
    % 绘制柱状图
    hold on;
    b1 = bar(x, [combined_data.SB(1:total_folders), combined_data.MB(1:total_folders), combined_data.CRS(1:total_folders)], ...
        'grouped');
    
    % 设置颜色
    set(b1(1), 'FaceColor', 'r', 'FaceAlpha', 0.6);
    set(b1(2), 'FaceColor', 'g', 'FaceAlpha', 0.6);
    set(b1(3), 'FaceColor', 'b', 'FaceAlpha', 0.6);
    
    % 设置图表属性
    title(sprintf('%s肠鸣音统计', period));
    xlabel('时间段（5分钟）');
    ylabel('数量');
    
    % 设置x轴刻度
    if total_folders > 0
        xticks(1:total_folders);
        xticklabels(arrayfun(@(x) sprintf('%d-%d', (x-1)*5, x*5), 1:total_folders, 'UniformOutput', false));
        xtickangle(45);
    end
    
    % 添加图例
    legend({'SB', 'MB', 'CRS'}, 'Location', 'northeastoutside');
    
    grid on;
    hold off;
end

% 调整图表布局
set(gcf, 'Color', 'white'); 

% % 添加新的图表，显示所有时间段的肠鸣音数据
% figure('Name', '肠鸣音活动统计', 'Position', [100 100 1500 400]);

% 准备数据
morning_data = struct('SB', results.morning.first.SB + results.morning.second.SB, ...
                     'MB', results.morning.first.MB + results.morning.second.MB, ...
                     'CRS', results.morning.first.CRS + results.morning.second.CRS);
afternoon_data = struct('SB', results.afternoon.first.SB + results.afternoon.second.SB, ...
                       'MB', results.afternoon.first.MB + results.afternoon.second.MB, ...
                       'CRS', results.afternoon.first.CRS + results.afternoon.second.CRS);

% 获取数据长度
morning_length = length(morning_data.SB);
afternoon_length = length(afternoon_data.SB);

% 将数据重组为10分钟间隔
morning_10min_length = floor(morning_length/2);
afternoon_10min_length = floor(afternoon_length/2);

% 创建10分钟间隔的数据数组
all_data_10min = zeros(morning_10min_length + afternoon_10min_length, 3);

% 合并上午数据为10分钟间隔
for i = 1:morning_10min_length
    idx = (i-1)*2 + 1;
    all_data_10min(i, 1) = sum(morning_data.SB(idx:min(idx+1, morning_length)));
    all_data_10min(i, 2) = sum(morning_data.MB(idx:min(idx+1, morning_length)));
    all_data_10min(i, 3) = sum(morning_data.CRS(idx:min(idx+1, morning_length)));
end

% 合并下午数据为10分钟间隔
for i = 1:afternoon_10min_length
    idx = (i-1)*2 + 1;
    all_data_10min(morning_10min_length + i, 1) = sum(afternoon_data.SB(idx:min(idx+1, afternoon_length)));
    all_data_10min(morning_10min_length + i, 2) = sum(afternoon_data.MB(idx:min(idx+1, afternoon_length)));
    all_data_10min(morning_10min_length + i, 3) = sum(afternoon_data.CRS(idx:min(idx+1, afternoon_length)));
end

% 调用新的绘图函数
plot_bowel_sounds(all_data_10min, ...                    % 数据
                 morning_10min_length, ...               % 上午数据长度
                 [200 200 1500 450], ...                % 图表位置和大小
                 'Bowel Sound Activity Over Time', ...   % 标题
                 [0.5 size(all_data_10min,1)+0.5], ...  % X轴范围
                 [0 max(all_data_10min(:))*1.1]);       % Y轴范围
% 
% % 保存图表
% saveas(gcf, '肠鸣音活动统计_10min.png');
% saveas(gcf, '肠鸣音活动统计_10min.fig');
