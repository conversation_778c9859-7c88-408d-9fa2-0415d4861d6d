%TEST_INTERMEDIATE_RESULTS 测试中间结果保存功能
%   该脚本用于测试修改后的信号处理流程，验证中间结果保存和分割功能。
%   可以在不处理大量文件的情况下快速验证新功能的正确性。
%
%   功能测试:
%   1. 配置参数验证
%   2. 中间结果保存功能
%   3. 文件夹结构创建
%   4. 分割功能测试
%   5. 文件命名规范验证
%
%   使用方法:
%   1. 确保有测试用的CSV文件
%   2. 运行此脚本进行功能验证
%   3. 检查生成的文件结构和内容
%
%   输出结构:
%   2、Processed data/
%   ├── bandpass_results/
%   │   ├── original_timeline/
%   │   └── reset_timeline/
%   ├── spectral_results/
%   │   ├── original_timeline/
%   │   └── reset_timeline/
%   ├── original_timeline/
%   └── reset_timeline/

clear all;
clc;
close all;

%% 添加函数路径
currentDir = fileparts(mfilename('fullpath'));
functionDir = fullfile(currentDir, '0、function', '2、Spectral Subtraction');
if exist(functionDir, 'dir')
    addpath(functionDir);
    fprintf('✓ 成功添加函数路径: %s\n', functionDir);
else
    error('函数文件夹不存在: %s', functionDir);
end

%% 创建测试配置
config = createProcessingConfig();

% 启用中间结果保存
config.saveIntermediateSteps = true;
config.enableIntermediateSegmentation = true;

% 设置较短的分割长度用于测试
config.secondarySegmentLength = 30;  % 30秒片段，便于测试
config.minSegmentLength = 5;         % 最小5秒

% 启用详细输出
config.enableVerboseOutput = true;
config.enableProgressDisplay = true;

fprintf('=== 测试配置 ===\n');
fprintf('中间结果保存: %s\n', config.saveIntermediateSteps ? '启用' : '禁用');
fprintf('中间结果分割: %s\n', config.enableIntermediateSegmentation ? '启用' : '禁用');
fprintf('带通滤波结果文件夹: %s\n', config.bandpassResultsFolder);
fprintf('谱减法结果文件夹: %s\n', config.spectralResultsFolder);
fprintf('分割长度: %.1f 秒\n', config.secondarySegmentLength);
fprintf('===============\n\n');

%% 选择测试文件
fprintf('请选择一个CSV文件进行测试...\n');
[fileName, pathName] = uigetfile('*.csv', '选择测试用的CSV文件');
if fileName == 0
    disp('用户取消了文件选择');
    return;
end

testFile = fullfile(pathName, fileName);
fprintf('选择的测试文件: %s\n\n', testFile);

%% 处理测试文件
try
    fprintf('=== 开始处理测试文件 ===\n');
    
    % 读取CSV文件
    data = readtable(testFile);
    data(1, :) = []; % 删除第一行
    
    % 验证CSV文件格式
    if size(data, 2) < max(config.requiredColumns)
        error('CSV文件列数不足');
    end
    
    % 提取第二列和第三列数据
    column2 = data{:, config.requiredColumns(1)};
    column3 = data{:, config.requiredColumns(2)};
    
    % 验证数据有效性
    if any(isnan(column2)) || any(isnan(column3))
        warning('数据包含NaN值，可能影响处理结果');
    end
    
    fprintf('数据长度: %d 样本 (%.2f 秒)\n', length(column2), length(column2)/config.samplingRate);
    
    %% 调用处理函数
    inputSignals = {column2, column3};
    [processedSignals, snrResults, intermediateResults] = processDualChannelSignals(inputSignals, config);
    
    %% 显示SNR结果
    fprintf('\n=== SNR结果 ===\n');
    fprintf('带通滤波后 column2 的 SNR = %.4f dB\n', snrResults.bandpassSNR(1));
    fprintf('带通滤波后 column3 的 SNR = %.4f dB\n', snrResults.bandpassSNR(2));
    if config.enableSpectralSubtraction
        fprintf('谱减滤波后 column2 的 SNR = %.4f dB\n', snrResults.spectralSubtractionSNR(1));
        fprintf('谱减滤波后 column3 的 SNR = %.4f dB\n', snrResults.spectralSubtractionSNR(2));
    end
    fprintf('===============\n\n');
    
    %% 创建时间表
    column2_processed = processedSignals{1};
    column3_processed = processedSignals{2};
    
    tt_ch2 = timetable(column2_processed, 'SampleRate', config.samplingRate);
    tt_ch3 = timetable(column3_processed, 'SampleRate', config.samplingRate);
    
    %% 创建输出文件夹
    outputFolder = fullfile(currentDir, 'test_output');
    if ~exist(outputFolder, 'dir')
        mkdir(outputFolder);
        fprintf('创建测试输出文件夹: %s\n', outputFolder);
    end
    
    %% 保存中间结果
    if config.saveIntermediateSteps
        fprintf('=== 保存中间结果 ===\n');
        saveIntermediateResults(intermediateResults, testFile, outputFolder, config);
    end
    
    %% 保存最终结果（用于对比）
    fprintf('=== 保存最终结果 ===\n');
    if config.enableSecondarySegmentation
        segmentAndSaveTimeTable(tt_ch2, tt_ch3, testFile, outputFolder, config);
    else
        [~, name, ~] = fileparts(testFile);
        finalFileName = fullfile(outputFolder, [name, '_final_tt.mat']);
        
        cleanName = regexprep(name, '[^a-zA-Z0-9_]', '_');
        if ~isempty(cleanName) && ~isletter(cleanName(1))
            cleanName = ['file_', cleanName];
        end
        
        var1Name = [cleanName, '_final_tt1'];
        var2Name = [cleanName, '_final_tt2'];
        
        eval([var1Name, ' = tt_ch2;']);
        eval([var2Name, ' = tt_ch3;']);
        save(finalFileName, var1Name, var2Name);
        
        fprintf('已保存最终结果: %s\n', [name, '_final_tt.mat']);
    end
    
    %% 验证输出结构
    fprintf('\n=== 验证输出结构 ===\n');
    verifyOutputStructure(outputFolder, config);
    
    fprintf('\n=== 测试完成 ===\n');
    fprintf('测试输出文件夹: %s\n', outputFolder);
    fprintf('请检查生成的文件结构和内容\n');
    
catch ME
    fprintf('\n=== 测试失败 ===\n');
    fprintf('错误信息: %s\n', ME.message);
    fprintf('错误位置: %s (第%d行)\n', ME.stack(1).name, ME.stack(1).line);
    rethrow(ME);
end

%% 清理路径
if exist('functionDir', 'var') && exist(functionDir, 'dir')
    rmpath(functionDir);
    fprintf('✓ 已清理函数路径\n');
end

function verifyOutputStructure(outputFolder, config)
%VERIFYOUTPUTSTRUCTURE 验证输出文件夹结构
    fprintf('检查输出文件夹结构...\n');
    
    % 检查主文件夹
    if exist(outputFolder, 'dir')
        fprintf('✓ 主输出文件夹存在: %s\n', outputFolder);
    else
        fprintf('✗ 主输出文件夹不存在\n');
        return;
    end
    
    % 检查中间结果文件夹
    if config.saveIntermediateSteps
        bandpassFolder = fullfile(outputFolder, config.bandpassResultsFolder);
        spectralFolder = fullfile(outputFolder, config.spectralResultsFolder);
        
        if exist(bandpassFolder, 'dir')
            fprintf('✓ 带通滤波结果文件夹存在: %s\n', config.bandpassResultsFolder);
            checkSubfolders(bandpassFolder, config);
        else
            fprintf('✗ 带通滤波结果文件夹不存在\n');
        end
        
        if exist(spectralFolder, 'dir')
            fprintf('✓ 谱减法结果文件夹存在: %s\n', config.spectralResultsFolder);
            checkSubfolders(spectralFolder, config);
        else
            fprintf('✗ 谱减法结果文件夹不存在\n');
        end
    end
    
    % 检查最终结果文件夹
    if config.enableSecondarySegmentation && config.enableDualTimeScale
        originalFolder = fullfile(outputFolder, config.originalTimelineFolder);
        resetFolder = fullfile(outputFolder, config.resetTimelineFolder);
        
        if exist(originalFolder, 'dir')
            fprintf('✓ 最终结果原始时间刻度文件夹存在: %s\n', config.originalTimelineFolder);
        end
        
        if exist(resetFolder, 'dir')
            fprintf('✓ 最终结果重置时间刻度文件夹存在: %s\n', config.resetTimelineFolder);
        end
    end
end

function checkSubfolders(parentFolder, config)
%CHECKSUBFOLDERS 检查子文件夹结构
    if config.enableIntermediateSegmentation && config.enableDualTimeScale
        originalFolder = fullfile(parentFolder, config.originalTimelineFolder);
        resetFolder = fullfile(parentFolder, config.resetTimelineFolder);
        
        if exist(originalFolder, 'dir')
            fprintf('  ✓ 原始时间刻度子文件夹存在\n');
        else
            fprintf('  ✗ 原始时间刻度子文件夹不存在\n');
        end
        
        if exist(resetFolder, 'dir')
            fprintf('  ✓ 重置时间刻度子文件夹存在\n');
        else
            fprintf('  ✗ 重置时间刻度子文件夹不存在\n');
        end
    end
end
