function savePreprocessedData(preprocessedSignals, originalFileName, outputFolder, config)
%SAVEPREPROCESSEDDATA 保存预处理数据转换结果
%   将经过预处理的CSV数据（去直流、去趋势、归一化）转换为时间表格式并保存，支持分割处理。
%   该函数确保预处理数据以与其他处理结果相同的格式保存，便于后续对比分析。
%
%   语法:
%   saveOriginalData(originalSignals, originalFileName, outputFolder, config)
%
%   输入参数:
%   originalSignals  - 原始信号数据 (元胞数组)
%                     {column2, column3} - CSV第2列和第3列的原始数据
%   originalFileName - 原始文件名（用于生成输出文件名）
%   outputFolder     - 主输出文件夹路径
%   config          - 处理配置参数结构体
%
%   输出结果:
%   在指定文件夹的子文件夹中生成原始数据文件：
%   - 1_original_data/: 原始数据转换结果
%     - original_timeline/: 原始时间刻度分割文件
%     - reset_timeline/: 重置时间刻度分割文件
%
%   文件命名格式:
%   - 完整文件: [原文件名]_original_tt.mat
%   - 分割文件: [原文件名]_original_seg[序号]_tt.mat
%   每个文件包含: [文件名]_tt1 (CSV第2列), [文件名]_tt2 (CSV第3列)
%
%   配置参数:
%   config.saveOriginalData          - 是否保存原始数据
%   config.originalDataFolder        - 原始数据文件夹名
%   config.enableStepSegmentation    - 是否对原始数据进行分割
%   config.samplingRate              - 采样率
%   其他分割相关配置参数...
%
%   处理流程:
%   1. 验证输入参数和原始数据
%   2. 创建原始数据文件夹结构
%   3. 将原始数据转换为时间表格式
%   4. 保存完整的原始数据文件（可选）
%   5. 如果启用分割，对原始数据进行分割处理
%   6. 生成处理报告和统计信息
%
%   示例:
%   % 基本用法
%   config = createProcessingConfig();
%   config.saveOriginalData = true;
%   config.enableStepSegmentation = true;
%   originalSignals = {column2, column3};
%   saveOriginalData(originalSignals, 'data1.csv', './output', config);
%
%   注意事项:
%   - 原始数据不进行任何预处理（归一化、滤波等）
%   - 分割参数与其他处理步骤保持一致
%   - 文件命名包含'original'标识
%   - 支持与其他步骤相同的双时间刻度功能
%
%   参见: SAVEINTERMEDIATERESULTS, SEGMENTANDSAVETIMETABLE, CREATEPROCESSINGCONFIG
%
%   作者: [作者姓名]
%   日期: [创建日期]
%   版本: 1.0

    %% 参数验证
    if nargin < 4
        error('saveOriginalData:NotEnoughInputs', '需要4个输入参数');
    end
    
    if ~config.saveOriginalData
        if config.enableVerboseOutput
            fprintf('原始数据保存已禁用，跳过处理\n');
        end
        return;
    end
    
    % 验证原始数据
    if ~iscell(originalSignals) || length(originalSignals) < 2
        error('saveOriginalData:InvalidOriginalSignals', ...
            '原始信号数据格式错误，需要包含两个通道的元胞数组');
    end
    
    %% 创建原始数据文件夹结构
    originalDataFolder = fullfile(outputFolder, config.originalDataFolder);
    
    % 创建主文件夹
    if ~exist(originalDataFolder, 'dir')
        mkdir(originalDataFolder);
        if config.enableVerboseOutput
            fprintf('创建原始数据文件夹: %s\n', originalDataFolder);
        end
    end
    
    %% 处理原始数据
    if config.enableVerboseOutput
        fprintf('\n=== 保存原始数据 ===\n');
    end
    
    try
        % 获取原始信号数据
        column2_original = originalSignals{1};
        column3_original = originalSignals{2};
        
        if isempty(column2_original) || isempty(column3_original)
            warning('saveOriginalData:EmptyOriginalData', ...
                '原始数据为空，跳过处理');
            return;
        end
        
        % 验证数据长度一致性
        if length(column2_original) ~= length(column3_original)
            error('saveOriginalData:DataLengthMismatch', ...
                '两个通道的原始数据长度不一致');
        end
        
        % 创建时间表（不进行任何预处理）
        tt_ch2_original = timetable(column2_original, 'SampleRate', config.samplingRate);
        tt_ch3_original = timetable(column3_original, 'SampleRate', config.samplingRate);
        
        % 生成文件名
        [~, baseName, ~] = fileparts(originalFileName);
        cleanBaseName = regexprep(baseName, '[^a-zA-Z0-9_]', '_');
        if ~isempty(cleanBaseName) && ~isletter(cleanBaseName(1))
            cleanBaseName = ['file_', cleanBaseName];
        end
        
        % 保存完整的原始数据文件（可选）
        if ~config.enableStepSegmentation
            % 只保存完整文件，不分割
            fullFileName = fullfile(originalDataFolder, [cleanBaseName, '_original_tt.mat']);
            
            % 创建变量名
            var1Name = [cleanBaseName, '_original_tt1'];
            var2Name = [cleanBaseName, '_original_tt2'];
            
            % 保存文件
            eval([var1Name, ' = tt_ch2_original;']);
            eval([var2Name, ' = tt_ch3_original;']);
            save(fullFileName, var1Name, var2Name);
            
            if config.enableProgressDisplay
                fprintf('已保存原始数据完整文件: %s\n', [cleanBaseName, '_original_tt.mat']);
            end
        else
            % 进行分割处理
            if config.enableVerboseOutput
                fprintf('对原始数据进行分割处理...\n');
            end
            
            % 调用分割函数，使用修改后的文件名前缀
            modifiedFileName = [cleanBaseName, '_original'];
            segmentAndSaveStepTimeTable(tt_ch2_original, tt_ch3_original, modifiedFileName, ...
                originalDataFolder, config, 'original');
        end
        
        if config.enableVerboseOutput
            dataLength = length(column2_original);
            dataDuration = dataLength / config.samplingRate;
            fprintf('原始数据长度: %d 样本 (%.2f 秒)\n', dataLength, dataDuration);
            fprintf('原始数据保存完成\n');
        end
        
    catch ME
        if config.enableErrorHandling
            warning('saveOriginalData:ProcessingFailed', ...
                '原始数据处理失败: %s', ME.message);
        else
            rethrow(ME);
        end
    end
    
    %% 输出总结信息
    if config.enableVerboseOutput
        fprintf('\n=== 原始数据保存完成 ===\n');
        fprintf('原始数据文件夹: %s\n', originalDataFolder);
        fprintf('========================\n\n');
    end
end
